#!/usr/bin/env python3
"""
Test script for the Supreme Court Decisions Scraper API
"""
import requests
import time
import json

API_BASE_URL = "http://localhost:8000"

def test_api():
    """Test the API endpoints"""

    print("🧪 Testing Supreme Court Decisions Scraper API")
    print("📝 Bu test API'nin çalıştığını doğrular ve küçük bir scraping testi yapar")
    print("⏱️  Rate limiting: 0.1s between decisions, 3s between pages, 5 retries")
    print("=" * 50)
    
    # Test root endpoint
    print("\n1. Testing root endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"✅ Root endpoint: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return
    
    # Test stats endpoint
    print("\n2. Testing stats endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/stats")
        print(f"✅ Stats endpoint: {response.status_code}")
        stats = response.json()
        print(f"   Total decisions: {stats['database_stats']['total_decisions']}")
        print(f"   Completed pages: {stats['scraping_stats']['completed_pages']}")
        print(f"   Incomplete pages: {stats['scraping_stats']['incomplete_pages']}")
    except Exception as e:
        print(f"❌ Stats endpoint failed: {e}")
    
    # Test auto-scraping (no page number specified)
    print("\n3. Testing auto-scrape endpoint...")
    try:
        scrape_data = {
            "page_size": 5  # Small size for testing, no page_number
        }
        response = requests.post(f"{API_BASE_URL}/scrape", json=scrape_data)
        print(f"✅ Auto-scrape endpoint: {response.status_code}")
        result = response.json()
        print(f"   Message: {result['message']}")
        print(f"   Page: {result['page_number']}")
        print(f"   Size: {result['page_size']}")

        page_number = result['page_number']
        
        # Wait a bit and check progress
        print(f"\n4. Checking progress for page {page_number}...")
        time.sleep(2)
        
        for i in range(10):  # Check progress up to 10 times
            try:
                response = requests.get(f"{API_BASE_URL}/progress/{page_number}")
                if response.status_code == 200:
                    progress = response.json()
                    print(f"   Progress: {progress['decision_index']}/{progress['total_decisions_in_page']} "
                          f"(Completed: {progress['is_completed']})")
                    
                    if progress['is_completed']:
                        print("   ✅ Page completed!")
                        break
                else:
                    print(f"   ⚠️ Progress check failed: {response.status_code}")
                
                time.sleep(3)  # Wait 3 seconds between checks
            except Exception as e:
                print(f"   ❌ Progress check error: {e}")
                break
        
    except Exception as e:
        print(f"❌ Scrape endpoint failed: {e}")
    
    # Test continuous scraping endpoint
    print("\n5. Testing continuous scraping endpoint...")
    try:
        response = requests.post(f"{API_BASE_URL}/start-continuous?page_size=3")
        print(f"✅ Continuous scraping endpoint: {response.status_code}")
        result = response.json()
        print(f"   Message: {result['message']}")
        print(f"   Start page: {result.get('start_page')}")
        print(f"   Note: {result.get('note')}")
    except Exception as e:
        print(f"❌ Continuous scraping endpoint failed: {e}")
    
    # Test failed decisions endpoint
    print("\n6. Testing failed decisions endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/failed-decisions")
        print(f"✅ Failed decisions endpoint: {response.status_code}")
        failed_data = response.json()
        print(f"   Total failed: {failed_data['total_failed']}")
        if failed_data['failed_by_reason']:
            print("   Failed by reason:")
            for reason_data in failed_data['failed_by_reason'][:3]:
                print(f"     • {reason_data['reason']}: {reason_data['count']}")
    except Exception as e:
        print(f"❌ Failed decisions endpoint failed: {e}")

    # Final stats
    print("\n7. Final stats...")
    try:
        response = requests.get(f"{API_BASE_URL}/stats")
        stats = response.json()
        print(f"   Total decisions: {stats['database_stats']['total_decisions']}")
        print(f"   Total failed: {stats['failed_decisions_stats']['total_failed']}")
        print(f"   Completed pages: {stats['scraping_stats']['completed_pages']}")
        print(f"   Recent decisions:")
        for decision in stats['database_stats']['recent_decisions'][:3]:
            print(f"     • {decision['decision_id']}: {decision['daire']}")
        if stats['failed_decisions_stats']['recent_failed']:
            print(f"   Recent failed:")
            for failed in stats['failed_decisions_stats']['recent_failed'][:2]:
                print(f"     • {failed['decision_id']}: {failed['failure_reason']}")
    except Exception as e:
        print(f"❌ Final stats failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API test completed!")

def test_continuous_scraping():
    """Test continuous scraping functionality"""

    print("🔄 Testing Continuous Scraping")
    print("📝 Bu test otomatik devam eden scraping'i test eder")
    print("⏱️  Rate limiting: 0.1s between decisions, 3s between pages")
    print("🔄 Retry policy: 5 retries for page IDs, no retries for decisions")
    print("=" * 60)

    try:
        # Start continuous scraping
        print("\n1. Starting continuous scraping...")
        response = requests.post(f"{API_BASE_URL}/start-continuous?page_size=3")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Continuous scraping started!")
            print(f"   Message: {result['message']}")
            print(f"   Start page: {result['start_page']}")
            print(f"   Start index: {result['start_index']}")
            print(f"   Rate limiting: {result['rate_limiting']}")

            start_page = result['start_page']

            # Monitor progress for a while
            print(f"\n2. Monitoring progress for page {start_page}...")
            for i in range(15):  # Monitor for up to 15 checks
                time.sleep(5)  # Wait 5 seconds between checks

                try:
                    progress_response = requests.get(f"{API_BASE_URL}/progress/{start_page}")
                    if progress_response.status_code == 200:
                        progress = progress_response.json()
                        print(f"   Progress: {progress['decision_index']}/{progress['total_decisions_in_page']} "
                              f"(Completed: {progress['is_completed']})")

                        if progress['is_completed']:
                            print(f"   ✅ Page {start_page} completed!")

                            # Check if next page started
                            next_page = start_page + 1
                            time.sleep(2)
                            next_progress_response = requests.get(f"{API_BASE_URL}/progress/{next_page}")
                            if next_progress_response.status_code == 200:
                                next_progress = next_progress_response.json()
                                print(f"   🚀 Next page {next_page} auto-started: {next_progress['decision_index']}/{next_progress['total_decisions_in_page']}")
                            break
                    else:
                        print(f"   ⚠️ Progress check failed: {progress_response.status_code}")

                except Exception as e:
                    print(f"   ❌ Progress check error: {e}")
                    break
        else:
            print(f"❌ Failed to start continuous scraping: {response.status_code}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Continuous scraping test failed: {e}")

    print("\n✅ Continuous scraping test completed!")

def scrape_multiple_pages(start_page: int, end_page: int, page_size: int = 10):
    """Scrape multiple pages using the API"""

    print(f"🚀 Scraping pages {start_page} to {end_page} with size {page_size}")
    print("=" * 60)

    for page in range(start_page, end_page + 1):
        print(f"\n📄 Starting page {page}...")

        try:
            scrape_data = {
                "page_number": page,
                "page_size": page_size
            }
            response = requests.post(f"{API_BASE_URL}/scrape", json=scrape_data)

            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {result['message']}")
            else:
                print(f"   ❌ Failed to start scraping: {response.status_code}")
                print(f"   Response: {response.text}")

        except Exception as e:
            print(f"   ❌ Error starting page {page}: {e}")

        # Small delay between requests
        time.sleep(1)

    print(f"\n✅ All pages ({start_page}-{end_page}) have been queued for scraping!")
    print("Use the progress endpoints to monitor their status.")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "multi":
            # Scrape multiple pages
            start = int(sys.argv[2]) if len(sys.argv) > 2 else 1
            end = int(sys.argv[3]) if len(sys.argv) > 3 else 5
            size = int(sys.argv[4]) if len(sys.argv) > 4 else 10
            scrape_multiple_pages(start, end, size)
        elif sys.argv[1] == "continuous":
            # Test continuous scraping
            test_continuous_scraping()
        else:
            print("Usage:")
            print("  python test_api.py                    # Run basic API tests")
            print("  python test_api.py multi [start] [end] [size]  # Scrape multiple pages")
            print("  python test_api.py continuous         # Test continuous scraping")
    else:
        test_api()
